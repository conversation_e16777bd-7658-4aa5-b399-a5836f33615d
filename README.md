# Loan Manager

Professional loan management software with Microsoft Office integration, built with Django 5.2.3 LTS, SQLite, and Next.js.

## Features

- **Loan Management**: Add, edit, and process loan applications
- **Rules Engine**: Configurable business rules with version history
- **Microsoft Office Integration**: Generate Word, Excel, and PowerPoint documents
- **Offline Deployment**: Packaged as executable for client deployment
- **Quality Attributes**: Accuracy, Inspectability, Learnability, Scalability

## Tech Stack

- **Backend**: Django 5.2.3 LTS + Django REST Framework
- **Database**: SQLite (for offline deployment)
- **Frontend**: Next.js 14 + React 18 + Ant Design
- **Packaging**: PyInstaller + MSI Installer
- **Development**: Docker + Docker Compose

## Development Setup

### Prerequisites
- Docker and Docker Compose
- Git

### Quick Start

1. Clone the repository:
```bash
git clone <repository-url>
cd loanManager
```

2. Start the development environment:
```bash
docker-compose up --build
```

3. Access the applications:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Django Admin: http://localhost:8000/admin

### Initial Setup

1. Create Django superuser:
```bash
docker-compose exec backend python manage.py createsuperuser
```

2. Run migrations:
```bash
docker-compose exec backend python manage.py migrate
```

## Project Structure

```
loanManager/
├── backend/                 # Django backend
│   ├── loanmanager/        # Main Django project
│   ├── loans/              # Loan management app
│   ├── rules_engine/       # Business rules engine
│   ├── office_integration/ # Microsoft Office integration
│   └── requirements.txt
├── frontend/               # Next.js frontend
│   ├── src/
│   │   └── app/           # App router pages
│   ├── package.json
│   └── next.config.js
└── docker-compose.yml     # Development environment
```

## Deployment

The application is designed for offline deployment using:
1. **PyInstaller**: Bundles Django backend into executable
2. **MSI Installer**: Professional Windows installation package
3. **Static Export**: Next.js frontend served by Django

## Quality Attributes

- **Accuracy**: Strong typing, validation, comprehensive testing
- **Inspectability**: Clear logging, debugging tools, transparent rules
- **Learnability**: Intuitive UI, comprehensive documentation
- **Scalability**: Modular architecture, efficient data handling
