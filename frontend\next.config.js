/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for offline packaging
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  // Disable server-side features for static export
  experimental: {
    esmExternals: false
  },
  // Configure for development vs production
  ...(process.env.NODE_ENV === 'development' && {
    output: undefined, // Disable static export in development
  })
}

module.exports = nextConfig
