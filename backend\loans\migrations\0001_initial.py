# Generated by Django 5.2.3 on 2025-08-05 16:00

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Borrower',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('legal_name', models.CharField(max_length=255, unique=True)),
                ('client_description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'borrowers',
                'ordering': ['legal_name'],
            },
        ),
        migrations.CreateModel(
            name='Broker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('targeted_strategies', 'Targeted Strategies'), ('stalwart', 'Stalwart'), ('kohr', 'KOHR')], max_length=100, unique=True)),
                ('type', models.CharField(blank=True, max_length=50)),
                ('active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'brokers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LoanType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('demand', 'Demand Loan'), ('term', 'Term Loan'), ('bridge', 'Bridge Loan'), ('promissory_note', 'Promissory Note')], max_length=50, unique=True)),
                ('description', models.TextField()),
                ('max_duration_months', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'loan_types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SeniorLender',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('capco', 'CAPCo'), ('bns', 'BNS'), ('equitable', 'Equitable'), ('bmo', 'BMO'), ('cibc', 'CIBC'), ('td', 'Toronto Dominion Bank')], max_length=100, unique=True)),
                ('tri_party_agreement', models.BooleanField(default=False)),
                ('active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'senior_lenders',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InterestType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('collection_method', models.CharField(choices=[('advance', 'Advance'), ('arrears', 'Arrears'), ('capitalized', 'Capitalized')], max_length=20)),
                ('frequency', models.CharField(choices=[('annually', 'Annually'), ('monthly', 'Monthly'), ('daily', 'Daily')], max_length=20)),
                ('rate_type', models.CharField(choices=[('fixed', 'Fixed'), ('floating', 'Floating')], max_length=20)),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'interest_types',
                'ordering': ['collection_method', 'frequency'],
                'unique_together': {('collection_method', 'frequency', 'rate_type')},
            },
        ),
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loan_number', models.CharField(max_length=50, unique=True)),
                ('maximum_commitment', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('loan_amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('advance_outstanding', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('spread', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=5)),
                ('open_date', models.DateField()),
                ('maturity_date', models.DateField()),
                ('policy_renewal_date', models.DateField(blank=True, null=True)),
                ('initial_loan_date', models.DateField()),
                ('special_clause', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('open', 'Open'), ('closed', 'Closed'), ('pending', 'Pending'), ('defaulted', 'Defaulted')], default='open', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('borrower', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='loans', to='loans.borrower')),
                ('broker', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='loans', to='loans.broker')),
                ('interest_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='loans', to='loans.interesttype')),
                ('loan_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='loans', to='loans.loantype')),
                ('senior_lender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='loans', to='loans.seniorlender')),
            ],
            options={
                'db_table': 'loans',
                'ordering': ['-open_date', 'loan_number'],
            },
        ),
        migrations.CreateModel(
            name='MarketRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rate_type', models.CharField(choices=[('prime', 'Prime Rate'), ('corra', 'CORRA Rate'), ('sunlife_dividend', 'Sunlife Dividend Scale'), ('prescribed', 'Canadian Prescribed Interest Rate')], max_length=30)),
                ('rate_value', models.DecimalField(decimal_places=4, max_digits=5)),
                ('rate_date', models.DateField()),
                ('source_url', models.URLField()),
                ('scraped_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'market_rates',
                'ordering': ['-rate_date', 'rate_type'],
                'indexes': [models.Index(fields=['rate_type', 'rate_date'], name='market_rate_rate_ty_552f4b_idx')],
                'unique_together': {('rate_type', 'rate_date')},
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField()),
                ('repayment_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('repayment_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('details', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='loans.loan')),
            ],
            options={
                'db_table': 'invoices',
                'ordering': ['-invoice_date'],
                'indexes': [models.Index(fields=['invoice_number'], name='invoices_invoice_7778bc_idx'), models.Index(fields=['loan', 'status'], name='invoices_loan_id_1fea0c_idx')],
            },
        ),
        migrations.CreateModel(
            name='InterestRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rate_source', models.CharField(choices=[('prime', 'Prime Rate'), ('corra', 'CORRA Rate'), ('prescribed', 'Prescribed Rate'), ('custom', 'Custom Rate')], max_length=20)),
                ('base_rate', models.DecimalField(decimal_places=4, max_digits=5)),
                ('spread', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=5)),
                ('total_rate', models.DecimalField(decimal_places=4, max_digits=5)),
                ('effective_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_current', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interest_rates', to='loans.loan')),
            ],
            options={
                'db_table': 'interest_rates',
                'ordering': ['-effective_date'],
                'indexes': [models.Index(fields=['loan', 'effective_date'], name='interest_ra_loan_id_e7beba_idx'), models.Index(fields=['is_current'], name='interest_ra_is_curr_708469_idx')],
            },
        ),
        migrations.CreateModel(
            name='InterestCalculation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('calculation_date', models.DateField()),
                ('daily_interest_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('running_interest_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('accrued_interest', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('deferred_interest', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('interest_received', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('balance_interest', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interest_calculations', to='loans.loan')),
            ],
            options={
                'db_table': 'interest_calculations',
                'ordering': ['-calculation_date'],
                'indexes': [models.Index(fields=['loan', 'calculation_date'], name='interest_ca_loan_id_a7cfdf_idx')],
                'unique_together': {('loan', 'calculation_date')},
            },
        ),
        migrations.CreateModel(
            name='Disbursement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('disbursement_date', models.DateField()),
                ('description', models.TextField()),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disbursements', to='loans.loan')),
            ],
            options={
                'db_table': 'disbursements',
                'ordering': ['-disbursement_date'],
                'indexes': [models.Index(fields=['loan', 'disbursement_date'], name='disbursemen_loan_id_584696_idx')],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('payment_date', models.DateField()),
                ('due_date', models.DateField()),
                ('payment_type', models.CharField(choices=[('principal', 'Principal'), ('interest', 'Interest'), ('both', 'Principal & Interest'), ('fee', 'Fee')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='loans.loan')),
            ],
            options={
                'db_table': 'payments',
                'ordering': ['-payment_date'],
                'indexes': [models.Index(fields=['loan', 'payment_date'], name='payments_loan_id_01d315_idx'), models.Index(fields=['status'], name='payments_status_d621e5_idx')],
            },
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('receipt_date', models.DateField()),
                ('description', models.TextField()),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='receipts', to='loans.loan')),
            ],
            options={
                'db_table': 'receipts',
                'ordering': ['-receipt_date'],
                'indexes': [models.Index(fields=['loan', 'receipt_date'], name='receipts_loan_id_bd1ba2_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='loan',
            index=models.Index(fields=['loan_number'], name='loans_loan_nu_a48674_idx'),
        ),
        migrations.AddIndex(
            model_name='loan',
            index=models.Index(fields=['status'], name='loans_status_9049a0_idx'),
        ),
        migrations.AddIndex(
            model_name='loan',
            index=models.Index(fields=['open_date'], name='loans_open_da_6d3454_idx'),
        ),
        migrations.AddIndex(
            model_name='loan',
            index=models.Index(fields=['borrower', 'status'], name='loans_borrowe_6654de_idx'),
        ),
    ]
