from django.core.management.base import BaseCommand
from loans.models import (
    <PERSON>roke<PERSON>, SeniorLender, LoanType, InterestType
)


class Command(BaseCommand):
    help = 'Populate initial reference data for the loan management system'

    def handle(self, *args, **options):
        self.stdout.write('Populating initial data...')

        # Create Brokers
        brokers = [
            ('targeted_strategies', 'Broker'),
            ('stalwart', 'Broker'),
            ('kohr', 'Broker'),
        ]
        
        for name, broker_type in brokers:
            broker, created = Broker.objects.get_or_create(
                name=name,
                defaults={'type': broker_type}
            )
            if created:
                self.stdout.write(f'Created broker: {broker.get_name_display()}')

        # Create Senior Lenders
        lenders = [
            ('capco', False),
            ('bns', True),
            ('equitable', True),
            ('bmo', True),
            ('cibc', True),
            ('td', False),
        ]
        
        for name, tri_party in lenders:
            lender, created = SeniorLender.objects.get_or_create(
                name=name,
                defaults={'tri_party_agreement': tri_party}
            )
            if created:
                self.stdout.write(f'Created senior lender: {lender.get_name_display()}')

        # Create Loan Types
        loan_types = [
            ('demand', 'Policy-backed loans, generally under 10 years', 120),
            ('term', 'Asset-backed loans, short-term in nature', 36),
            ('bridge', 'Used when senior lender cannot fund initially, generally less than 3 months', 3),
            ('promissory_note', 'Variable length promissory notes', None),
        ]
        
        for name, description, max_duration in loan_types:
            loan_type, created = LoanType.objects.get_or_create(
                name=name,
                defaults={
                    'description': description,
                    'max_duration_months': max_duration
                }
            )
            if created:
                self.stdout.write(f'Created loan type: {loan_type.get_name_display()}')

        # Create Interest Types
        interest_types = [
            ('advance', 'annually', 'fixed', 'Interest collected in advance annually, fixed rate for entire policy period'),
            ('arrears', 'annually', 'floating', 'Interest collected in arrears annually, generally floating rate'),
            ('arrears', 'monthly', 'floating', 'Interest collected in arrears monthly, generally floating rate'),
            ('capitalized', 'annually', 'floating', 'Interest capitalized with special clauses, generally floating rate'),
        ]
        
        for collection, frequency, rate_type, description in interest_types:
            interest_type, created = InterestType.objects.get_or_create(
                collection_method=collection,
                frequency=frequency,
                rate_type=rate_type,
                defaults={'description': description}
            )
            if created:
                self.stdout.write(f'Created interest type: {interest_type}')

        self.stdout.write(self.style.SUCCESS('Successfully populated initial data!'))
