{"name": "loanmanager-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export"}, "dependencies": {"next": "14.0.4", "react": "18.2.0", "react-dom": "18.2.0", "antd": "5.12.8", "@ant-design/icons": "5.2.6"}, "devDependencies": {"typescript": "5.3.3", "@types/node": "20.10.5", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "eslint": "8.56.0", "eslint-config-next": "14.0.4"}}