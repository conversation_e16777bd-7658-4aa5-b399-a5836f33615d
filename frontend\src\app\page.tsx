export default function Home() {
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Loan Manager</h1>
      <p>Professional loan management software with Microsoft Office integration</p>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px', marginTop: '32px' }}>
        <div style={{ border: '1px solid #d9d9d9', borderRadius: '8px', padding: '16px' }}>
          <h3>Add New Loan</h3>
          <p>Create a new loan application with all required documentation</p>
        </div>

        <div style={{ border: '1px solid #d9d9d9', borderRadius: '8px', padding: '16px' }}>
          <h3>Manage Loans</h3>
          <p>View, edit, and process existing loan applications</p>
        </div>

        <div style={{ border: '1px solid #d9d9d9', borderRadius: '8px', padding: '16px' }}>
          <h3>Reports & Analytics</h3>
          <p>Generate reports and analyze loan portfolio performance</p>
        </div>
      </div>

      <div style={{ marginTop: '48px', textAlign: 'center' }}>
        <button style={{ marginRight: '8px', padding: '8px 16px', backgroundColor: '#1890ff', color: 'white', border: 'none', borderRadius: '4px' }}>
          Quick Add Loan
        </button>
        <button style={{ padding: '8px 16px', backgroundColor: 'white', color: '#1890ff', border: '1px solid #1890ff', borderRadius: '4px' }}>
          View All Loans
        </button>
      </div>
    </div>
  )
}
